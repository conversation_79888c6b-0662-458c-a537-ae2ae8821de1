import React, { useState, useEffect } from 'react';
import { Dialog } from 'primereact/dialog';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Button } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { InputTextarea } from 'primereact/inputtextarea';
import { Dropdown } from 'primereact/dropdown';
import { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';
import { motion } from 'framer-motion';
import { FiUsers, FiPlus, FiEdit, FiTrash2, FiSearch, FiMail, FiPhone, FiUser, FiCheck, FiX } from 'react-icons/fi';
import { ToggleButton } from 'primereact/togglebutton';
import { Image } from 'primereact/image';
import { useLayout } from '@contexts/LayoutContext';
import { mockEventMembers, getEventMembersByGroupId } from '@data/mockEventGroupsData';

const AttendeesModal = ({ visible, onHide, event, onUpdateEvent }) => {
    const { isMobile } = useLayout();

    // State management
    const [attendees, setAttendees] = useState([]);
    const [loading, setLoading] = useState(false);
    const [globalFilter, setGlobalFilter] = useState('');
    const [showAddAttendeeModal, setShowAddAttendeeModal] = useState(false);
    const [editingAttendee, setEditingAttendee] = useState(null);

    // Get the default attendees group
    const attendeesGroup = event?.associatedGroups?.find(group => group.is_default_attendees);

    useEffect(() => {
        if (attendeesGroup) {
            // Load attendees for the default attendees group
            const groupAttendees = getEventMembersByGroupId(attendeesGroup.id);
            setAttendees(groupAttendees);
        }
    }, [attendeesGroup]);

    // Early return after hooks
    if (!event) return null;

    // Handle adding new attendee
    const handleAddAttendee = async (attendeeData) => {
        try {
            setLoading(true);
            
            const newAttendee = {
                id: `evt-mem-${Date.now()}`,
                name: attendeeData.name,
                email: attendeeData.email || '',
                phone: attendeeData.phone || '',
                department: attendeeData.department || '',
                role: attendeeData.role || '',
                event_id: event.id,
                group_id: attendeesGroup.id,
                image: null,
                event_context: true,
                access_level: "standard",
                addedAt: new Date().toISOString()
            };

            setAttendees(prev => [...prev, newAttendee]);
            
            // Update the event's attendees count
            const updatedEvent = {
                ...event,
                currentAttendees: event.currentAttendees + 1,
                associatedGroups: event.associatedGroups.map(group =>
                    group.is_default_attendees 
                        ? { ...group, memberCount: group.memberCount + 1 }
                        : group
                )
            };

            if (onUpdateEvent) {
                onUpdateEvent(updatedEvent);
            }

            setShowAddAttendeeModal(false);
        } catch (error) {
            console.error('Error adding attendee:', error);
        } finally {
            setLoading(false);
        }
    };

    // Handle editing attendee
    const handleEditAttendee = async (attendeeData) => {
        try {
            setLoading(true);
            
            const updatedAttendees = attendees.map(attendee =>
                attendee.id === editingAttendee.id
                    ? { ...attendee, ...attendeeData }
                    : attendee
            );

            setAttendees(updatedAttendees);
            setEditingAttendee(null);
        } catch (error) {
            console.error('Error updating attendee:', error);
        } finally {
            setLoading(false);
        }
    };

    // Handle deleting attendee
    const handleDeleteAttendee = (attendee) => {
        confirmDialog({
            message: `Are you sure you want to remove ${attendee.name} from the event?`,
            header: 'Remove Attendee',
            icon: 'pi pi-exclamation-triangle',
            acceptClassName: 'p-button-danger',
            accept: () => {
                setAttendees(prev => prev.filter(a => a.id !== attendee.id));
                
                // Update the event's attendees count
                const updatedEvent = {
                    ...event,
                    currentAttendees: Math.max(0, event.currentAttendees - 1),
                    associatedGroups: event.associatedGroups.map(group =>
                        group.is_default_attendees 
                            ? { ...group, memberCount: Math.max(0, group.memberCount - 1) }
                            : group
                    )
                };

                if (onUpdateEvent) {
                    onUpdateEvent(updatedEvent);
                }
            }
        });
    };

    const handleAttendanceToggle = (attendeeId, confirmed) => {
        setAttendees(prev => prev.map(attendee =>
            attendee.id === attendeeId
                ? { ...attendee, attendance_confirmed: confirmed }
                : attendee
        ));
        // Here you would typically make an API call to update attendance status
    };

    // Table templates
    const nameBodyTemplate = (rowData) => (
        <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-full overflow-hidden border-2 border-gray-200">
                {rowData.image ? (
                    <Image
                        src={rowData.image}
                        alt={`${rowData.name} profile`}
                        imageClassName="w-full h-full object-cover"
                        width="40"
                        height="40"
                        onError={(e) => {
                            e.target.style.display = 'none';
                            e.target.nextSibling.style.display = 'flex';
                        }}
                    />
                ) : null}
                <div className={`w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center ${rowData.image ? 'hidden' : ''}`}>
                    <span className="text-white font-bold text-sm">
                        {rowData.name?.charAt(0)?.toUpperCase()}
                    </span>
                </div>
            </div>
            <div>
                <div className="font-medium text-gray-900">{rowData.name}</div>
                <div className="text-sm text-gray-500">{rowData.role}</div>
            </div>
        </div>
    );

    const emailBodyTemplate = (rowData) => (
        <div className="flex items-center gap-2">
            <FiMail size={14} className="text-gray-400" />
            <span className="text-gray-700">{rowData.email || 'N/A'}</span>
        </div>
    );

    const phoneBodyTemplate = (rowData) => (
        <div className="flex items-center gap-2">
            <FiPhone size={14} className="text-gray-400" />
            <span className="text-gray-700">{rowData.phone || 'N/A'}</span>
        </div>
    );

    const attendanceBodyTemplate = (rowData) => (
        <div className="flex items-center justify-center">
            <ToggleButton
                checked={rowData.attendance_confirmed || false}
                onChange={(e) => handleAttendanceToggle(rowData.id, e.value)}
                onLabel=""
                offLabel=""
                onIcon={<FiCheck size={14} />}
                offIcon={<FiX size={14} />}
                className={`w-12 h-6 ${rowData.attendance_confirmed ? 'bg-green-500' : 'bg-gray-300'}`}
                style={{
                    width: '48px',
                    height: '24px'
                }}
            />
        </div>
    );

    const actionBodyTemplate = (rowData) => (
        <div className="flex gap-2">
            <Button
                icon={<FiEdit size={14} />}
                className="p-button-sm p-button-text"
                onClick={() => setEditingAttendee(rowData)}
                tooltip="Edit Attendee"
                tooltipOptions={{ position: 'top' }}
            />
            <Button
                icon={<FiTrash2 size={14} />}
                className="p-button-sm p-button-text p-button-danger"
                onClick={() => handleDeleteAttendee(rowData)}
                tooltip="Remove Attendee"
                tooltipOptions={{ position: 'top' }}
            />
        </div>
    );

    // Mobile attendees list
    const MobileAttendeesList = () => (
        <div className="space-y-4">
            {attendees.map((attendee) => (
                <motion.div
                    key={attendee.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                    className="border border-gray-200 rounded-lg p-4 bg-white"
                >
                    <div className="flex items-start gap-3 mb-3">
                        <div className="w-12 h-12 rounded-full overflow-hidden border-2 border-gray-200">
                            {attendee.image ? (
                                <Image
                                    src={attendee.image}
                                    alt={`${attendee.name} profile`}
                                    imageClassName="w-full h-full object-cover"
                                    width="48"
                                    height="48"
                                    onError={(e) => {
                                        e.target.style.display = 'none';
                                        e.target.nextSibling.style.display = 'flex';
                                    }}
                                />
                            ) : null}
                            <div className={`w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center ${attendee.image ? 'hidden' : ''}`}>
                                <span className="text-white font-bold text-lg">
                                    {attendee.name?.charAt(0)?.toUpperCase()}
                                </span>
                            </div>
                        </div>
                        <div className="flex-1">
                            <div className="flex items-start justify-between mb-1">
                                <h3 className="font-semibold text-gray-900">{attendee.name}</h3>
                                <div className="flex gap-1">
                                    <Button
                                        icon={<FiEdit size={14} />}
                                        className="p-button-sm p-button-text"
                                        onClick={() => setEditingAttendee(attendee)}
                                    />
                                    <Button
                                        icon={<FiTrash2 size={14} />}
                                        className="p-button-sm p-button-text p-button-danger"
                                        onClick={() => handleDeleteAttendee(attendee)}
                                    />
                                </div>
                            </div>
                            <p className="text-sm text-gray-600 mb-2">{attendee.role}</p>
                            <div className="space-y-2">
                                {attendee.email && (
                                    <div className="flex items-center gap-2 text-sm text-gray-500">
                                        <FiMail size={12} />
                                        <span>{attendee.email}</span>
                                    </div>
                                )}
                                {attendee.phone && (
                                    <div className="flex items-center gap-2 text-sm text-gray-500">
                                        <FiPhone size={12} />
                                        <span>{attendee.phone}</span>
                                    </div>
                                )}
                                <div className="flex items-center justify-between pt-2 border-t border-gray-100">
                                    <span className="text-sm text-gray-600">Attendance Confirmed:</span>
                                    <ToggleButton
                                        checked={attendee.attendance_confirmed || false}
                                        onChange={(e) => handleAttendanceToggle(attendee.id, e.value)}
                                        onLabel=""
                                        offLabel=""
                                        onIcon={<FiCheck size={12} />}
                                        offIcon={<FiX size={12} />}
                                        className={`w-10 h-5 ${attendee.attendance_confirmed ? 'bg-green-500' : 'bg-gray-300'}`}
                                        style={{
                                            width: '40px',
                                            height: '20px'
                                        }}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </motion.div>
            ))}
        </div>
    );

    return (
        <>
            <Dialog
                header={
                    <div className="flex items-center justify-between w-full">
                        <div className="flex items-center gap-2">
                            <FiUsers className="text-blue-600" size={20} />
                            <span>Event Attendees - {event.name}</span>
                        </div>
                        <Button
                            icon={<FiPlus size={16} />}
                            label={isMobile ? "" : "Add Attendee"}
                            className="p-button-sm"
                            style={{
                                backgroundColor: 'white',
                                color: 'black',
                                border: '1px solid #d1d5db',
                                padding: isMobile ? '8px' : '6px 12px',
                                borderRadius: '6px',
                                transition: 'all 0.2s ease',
                                minHeight: '44px',
                                minWidth: isMobile ? '44px' : 'auto'
                            }}
                            onMouseEnter={(e) => {
                                if (!isMobile) {
                                    e.target.style.transform = 'translateY(-1px)';
                                    e.target.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';
                                }
                            }}
                            onMouseLeave={(e) => {
                                if (!isMobile) {
                                    e.target.style.transform = 'translateY(0)';
                                    e.target.style.boxShadow = 'none';
                                }
                            }}
                            onClick={() => setShowAddAttendeeModal(true)}
                            tooltip="Add New Attendee"
                            tooltipOptions={{ position: 'bottom' }}
                        />
                    </div>
                }
                visible={visible}
                style={{
                    width: isMobile ? '95vw' : '80vw',
                    maxWidth: isMobile ? '95vw' : '1200px',
                    height: isMobile ? '90vh' : 'auto',
                    maxHeight: '90vh'
                }}
                breakpoints={{
                    '960px': '95vw',
                    '641px': '95vw'
                }}
                onHide={onHide}
                className="attendees-modal"
                contentStyle={{
                    height: isMobile ? 'calc(90vh - 80px)' : 'auto',
                    overflow: 'auto',
                    padding: isMobile ? '15px' : '20px'
                }}
                modal
            >
                <div className="space-y-6">
                    {/* Summary Section */}
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3 }}
                        className="bg-gray-50 p-4 rounded-lg"
                    >
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div className="text-center">
                                <div className="text-2xl font-bold text-blue-600">{attendees.length}</div>
                                <div className="text-sm text-gray-600">Current Attendees</div>
                            </div>
                            <div className="text-center">
                                <div className="text-2xl font-bold text-gray-800">{event.maxAttendees}</div>
                                <div className="text-sm text-gray-600">Maximum Capacity</div>
                            </div>
                            <div className="text-center">
                                <div className="text-2xl font-bold text-green-600">
                                    {Math.max(0, event.maxAttendees - attendees.length)}
                                </div>
                                <div className="text-sm text-gray-600">Available Spots</div>
                            </div>
                        </div>
                    </motion.div>

                    {/* Search Section */}
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: 0.1 }}
                        className="flex items-center gap-4"
                    >
                        <div className="flex-1 relative">
                            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                            <InputText
                                value={globalFilter}
                                onChange={(e) => setGlobalFilter(e.target.value)}
                                placeholder="Search attendees..."
                                className="w-full pl-10"
                            />
                        </div>
                    </motion.div>

                    {/* Attendees List */}
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: 0.2 }}
                    >
                        {isMobile ? (
                            <MobileAttendeesList />
                        ) : (
                            <DataTable
                                value={attendees}
                                loading={loading}
                                globalFilter={globalFilter}
                                emptyMessage="No attendees found"
                                paginator
                                rows={10}
                                rowsPerPageOptions={[5, 10, 25]}
                                className="border border-gray-200 rounded-lg"
                            >
                                <Column body={nameBodyTemplate} header="Name" sortable sortField="name" style={{ minWidth: '200px' }} />
                                <Column body={emailBodyTemplate} header="Email" sortable sortField="email" style={{ minWidth: '200px' }} />
                                <Column body={phoneBodyTemplate} header="Phone" sortable sortField="phone" style={{ minWidth: '150px' }} />
                                <Column body={attendanceBodyTemplate} header="Confirmed" style={{ minWidth: '100px', textAlign: 'center' }} />
                                <Column body={actionBodyTemplate} exportable={false} style={{ minWidth: '8rem' }} />
                            </DataTable>
                        )}
                    </motion.div>
                </div>
            </Dialog>

            {/* Add/Edit Attendee Modal */}
            <AttendeeFormModal
                visible={showAddAttendeeModal || !!editingAttendee}
                onHide={() => {
                    setShowAddAttendeeModal(false);
                    setEditingAttendee(null);
                }}
                onSave={editingAttendee ? handleEditAttendee : handleAddAttendee}
                attendeeData={editingAttendee}
                isEdit={!!editingAttendee}
                loading={loading}
            />

            <ConfirmDialog />
        </>
    );
};

// Attendee Form Modal Component
const AttendeeFormModal = ({ visible, onHide, onSave, attendeeData, isEdit = false, loading }) => {
    const { isMobile } = useLayout();
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        phone: '',
        department: '',
        role: '',
        attendance_confirmed: false
    });
    const [errors, setErrors] = useState({});

    useEffect(() => {
        if (attendeeData && isEdit) {
            setFormData({
                name: attendeeData.name || '',
                email: attendeeData.email || '',
                phone: attendeeData.phone || '',
                department: attendeeData.department || '',
                role: attendeeData.role || '',
                attendance_confirmed: attendeeData.attendance_confirmed || false
            });
        } else {
            setFormData({
                name: '',
                email: '',
                phone: '',
                department: '',
                role: '',
                attendance_confirmed: false
            });
        }
        setErrors({});
    }, [attendeeData, isEdit, visible]);

    const validateForm = () => {
        const newErrors = {};
        
        if (!formData.name.trim()) {
            newErrors.name = 'Name is required';
        }
        
        if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
            newErrors.email = 'Please enter a valid email address';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = () => {
        if (validateForm()) {
            onSave(formData);
        }
    };

    const departmentOptions = [
        { label: 'Marketing', value: 'Marketing' },
        { label: 'Sales', value: 'Sales' },
        { label: 'Engineering', value: 'Engineering' },
        { label: 'HR', value: 'HR' },
        { label: 'Finance', value: 'Finance' },
        { label: 'Operations', value: 'Operations' },
        { label: 'IT', value: 'IT' },
        { label: 'External', value: 'External' }
    ];

    return (
        <Dialog
            header={isEdit ? 'Edit Attendee' : 'Add New Attendee'}
            visible={visible}
            style={{
                width: isMobile ? '95vw' : '50vw',
                maxWidth: isMobile ? '95vw' : '600px'
            }}
            breakpoints={{
                '960px': '80vw',
                '641px': '95vw'
            }}
            onHide={onHide}
            modal
        >
            <div className="space-y-4">
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                        Name <span className="text-red-500">*</span>
                    </label>
                    <InputText
                        value={formData.name}
                        onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                        placeholder="Enter attendee name"
                        className={`w-full ${errors.name ? 'p-invalid' : ''}`}
                    />
                    {errors.name && <small className="p-error">{errors.name}</small>}
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                    <InputText
                        value={formData.email}
                        onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                        placeholder="Enter email address"
                        className={`w-full ${errors.email ? 'p-invalid' : ''}`}
                    />
                    {errors.email && <small className="p-error">{errors.email}</small>}
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                    <InputText
                        value={formData.phone}
                        onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                        placeholder="Enter phone number"
                        className="w-full"
                    />
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Department</label>
                    <Dropdown
                        value={formData.department}
                        onChange={(e) => setFormData(prev => ({ ...prev, department: e.value }))}
                        options={departmentOptions}
                        placeholder="Select department"
                        className="w-full"
                        showClear
                    />
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Role</label>
                    <InputText
                        value={formData.role}
                        onChange={(e) => setFormData(prev => ({ ...prev, role: e.target.value }))}
                        placeholder="Enter role/position"
                        className="w-full"
                    />
                </div>

                <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                    <label className="block text-sm font-medium text-gray-700">Attendance Confirmed</label>
                    <ToggleButton
                        checked={formData.attendance_confirmed}
                        onChange={(e) => setFormData(prev => ({ ...prev, attendance_confirmed: e.value }))}
                        onLabel=""
                        offLabel=""
                        onIcon={<FiCheck size={14} />}
                        offIcon={<FiX size={14} />}
                        className={`w-12 h-6 ${formData.attendance_confirmed ? 'bg-green-500' : 'bg-gray-300'}`}
                        style={{
                            width: '48px',
                            height: '24px'
                        }}
                    />
                </div>

                <div className="flex gap-3 pt-4">
                    <Button
                        label="Cancel"
                        className="p-button-outlined flex-1"
                        onClick={onHide}
                        disabled={loading}
                    />
                    <Button
                        label={isEdit ? 'Update' : 'Add'}
                        className="flex-1"
                        onClick={handleSubmit}
                        loading={loading}
                    />
                </div>
            </div>
        </Dialog>
    );
};

export default AttendeesModal;
